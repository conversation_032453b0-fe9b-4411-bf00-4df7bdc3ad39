@import "tailwindcss";

html {
  scroll-behavior: smooth;
}

:root {
  --background: #ffffff;
  --foreground: #171717;
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

body {
  background: #ffffff;
  color: #171717;
  font-family: var(--font-poppins), Arial, Helvetica, sans-serif;
}

.font-logo {
  font-family: 'Jost', sans-serif !important;
  font-weight: 700 !important;
  letter-spacing: 0.02em !important;
}

/* Enhanced backdrop blur for modals */
.modal-backdrop {
  backdrop-filter: blur(12px) !important;
  -webkit-backdrop-filter: blur(12px) !important;
  background-color: rgba(0, 0, 0, 0.6) !important;
  pointer-events: auto !important;
}

/* Primary color class */
.color-primary {
  background: linear-gradient(135deg, #FFD700, #FFA500, #FFD700) !important;
  -webkit-background-clip: text !important;
  -webkit-text-fill-color: transparent !important;
  background-clip: text !important;
}

.section-heading {
  font-weight: 700;
  font-size: 2.5rem;
  line-height: 1.2;
  margin-bottom: 1.5rem;
}

.text-capitalize {
  text-transform: capitalize;
}
